# Ignore Maven & Gradle directories
target/
build/

# Ignore Maven & Gradle specific files
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
*.log
*.gz
*.war
*.ear

# Eclipse
.project
.classpath
.settings/

# IDEA
.idea/
*.iml
*.iws

# VS Code
.vscode/

# OS specific
.DS_Store
Thumbs.db

# Logs
*.log

# Spring Boot specific
spring.log

# Application properties
# You might want to ignore application.properties if it contains sensitive information
application.properties
application.yml
application-*.properties
application-*.yml

# Database
*.h2.db
*.mv.db

# Node modules if you have frontend files
node_modules/

# Test reports
test-output/
target/
