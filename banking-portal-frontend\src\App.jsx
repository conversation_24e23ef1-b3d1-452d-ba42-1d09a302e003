import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';

// Auth Components
import Login from './components/auth/Login';
import Register from './components/auth/Register';
import LandingPage from './components/LandingPage';

// Dashboard Components
import Dashboard from './components/dashboard/Dashboard';

// Banking Components
import CashDeposit from './components/banking/CashDeposit';
import CashWithdrawal from './components/banking/CashWithdrawal';
import FundTransfer from './components/banking/FundTransfer';
import Transactions from './components/banking/Transactions';

// Account Components
import AccountManagement from './components/account/AccountManagement';

// Profile Components
import Profile from './components/profile/Profile';

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="App">
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />

            {/* Protected Routes */}
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />

            <Route path="/deposit" element={
              <ProtectedRoute>
                <CashDeposit />
              </ProtectedRoute>
            } />

            <Route path="/withdraw" element={
              <ProtectedRoute>
                <CashWithdrawal />
              </ProtectedRoute>
            } />

            <Route path="/transfer" element={
              <ProtectedRoute>
                <FundTransfer />
              </ProtectedRoute>
            } />

            <Route path="/transactions" element={
              <ProtectedRoute>
                <Transactions />
              </ProtectedRoute>
            } />

            <Route path="/account" element={
              <ProtectedRoute>
                <AccountManagement />
              </ProtectedRoute>
            } />

            <Route path="/profile" element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            } />

            <Route path="/settings" element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            } />

            {/* Landing page */}
            <Route path="/" element={<LandingPage />} />

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  );
}

export default App;
