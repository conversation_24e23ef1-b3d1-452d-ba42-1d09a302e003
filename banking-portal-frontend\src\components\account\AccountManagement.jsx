import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { accountAPI } from '../../services/api';
import Sidebar from '../dashboard/Sidebar';
import { CreditCardIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

const AccountManagement = () => {
  const [activeTab, setActiveTab] = useState('create-pin');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPins, setShowPins] = useState({
    currentPin: false,
    newPin: false,
    confirmPin: false,
  });

  const createPinForm = useForm();
  const updatePinForm = useForm();

  const onCreatePin = async (data) => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      await accountAPI.createPin({
        password: data.password,
        pin: data.pin,
      });
      
      setSuccess('PIN created successfully!');
      createPinForm.reset();
    } catch (err) {
      setError(err.response?.data || 'Failed to create PIN. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const onUpdatePin = async (data) => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      await accountAPI.updatePin({
        password: data.password,
        oldPin: data.currentPin,
        newPin: data.newPin,
      });
      
      setSuccess('PIN updated successfully!');
      updatePinForm.reset();
    } catch (err) {
      setError(err.response?.data || 'Failed to update PIN. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const togglePinVisibility = (field) => {
    setShowPins(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden md:ml-64">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              <CreditCardIcon className="h-8 w-8 text-orange-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Account Management</h1>
                <p className="text-sm text-gray-600">Manage your account PIN and security settings</p>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            {/* Tab Navigation */}
            <div className="mb-6">
              <nav className="flex space-x-8">
                <button
                  onClick={() => setActiveTab('create-pin')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'create-pin'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Create PIN
                </button>
                <button
                  onClick={() => setActiveTab('update-pin')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'update-pin'
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Update PIN
                </button>
              </nav>
            </div>

            {error && (
              <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}
            
            {success && (
              <div className="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                {success}
              </div>
            )}

            {/* Create PIN Tab */}
            {activeTab === 'create-pin' && (
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New PIN</h3>
                <form onSubmit={createPinForm.handleSubmit(onCreatePin)} className="space-y-6">
                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                      Account Password
                    </label>
                    <input
                      {...createPinForm.register('password', {
                        required: 'Password is required',
                        minLength: {
                          value: 8,
                          message: 'Password must be at least 8 characters'
                        }
                      })}
                      type="password"
                      className="input-field mt-1"
                      placeholder="Enter your account password"
                    />
                    {createPinForm.formState.errors.password && (
                      <p className="mt-1 text-sm text-red-600">
                        {createPinForm.formState.errors.password.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="pin" className="block text-sm font-medium text-gray-700">
                      New PIN
                    </label>
                    <div className="mt-1 relative">
                      <input
                        {...createPinForm.register('pin', {
                          required: 'PIN is required',
                          pattern: {
                            value: /^[0-9]{4}$/,
                            message: 'PIN must be exactly 4 digits'
                          }
                        })}
                        type={showPins.newPin ? 'text' : 'password'}
                        maxLength="4"
                        className="input-field pr-10"
                        placeholder="Enter 4-digit PIN"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => togglePinVisibility('newPin')}
                      >
                        {showPins.newPin ? (
                          <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                        ) : (
                          <EyeIcon className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    {createPinForm.formState.errors.pin && (
                      <p className="mt-1 text-sm text-red-600">
                        {createPinForm.formState.errors.pin.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="confirmPin" className="block text-sm font-medium text-gray-700">
                      Confirm PIN
                    </label>
                    <div className="mt-1 relative">
                      <input
                        {...createPinForm.register('confirmPin', {
                          required: 'Please confirm your PIN',
                          validate: value => 
                            value === createPinForm.watch('pin') || 'PINs do not match'
                        })}
                        type={showPins.confirmPin ? 'text' : 'password'}
                        maxLength="4"
                        className="input-field pr-10"
                        placeholder="Confirm your PIN"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => togglePinVisibility('confirmPin')}
                      >
                        {showPins.confirmPin ? (
                          <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                        ) : (
                          <EyeIcon className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    {createPinForm.formState.errors.confirmPin && (
                      <p className="mt-1 text-sm text-red-600">
                        {createPinForm.formState.errors.confirmPin.message}
                      </p>
                    )}
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h4 className="text-sm font-medium text-blue-900 mb-2">PIN Security Guidelines:</h4>
                    <ul className="text-sm text-blue-800 space-y-1">
                      <li>• PIN must be exactly 4 digits</li>
                      <li>• Choose a PIN that's not easily guessable</li>
                      <li>• Don't use sequential numbers (1234) or repeated digits (1111)</li>
                      <li>• Keep your PIN confidential and don't share it with anyone</li>
                    </ul>
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="btn-primary w-full flex justify-center items-center"
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Creating PIN...
                      </>
                    ) : (
                      'Create PIN'
                    )}
                  </button>
                </form>
              </div>
            )}

            {/* Update PIN Tab */}
            {activeTab === 'update-pin' && (
              <div className="card">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Update Existing PIN</h3>
                <form onSubmit={updatePinForm.handleSubmit(onUpdatePin)} className="space-y-6">
                  <div>
                    <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                      Account Password
                    </label>
                    <input
                      {...updatePinForm.register('password', {
                        required: 'Password is required',
                        minLength: {
                          value: 8,
                          message: 'Password must be at least 8 characters'
                        }
                      })}
                      type="password"
                      className="input-field mt-1"
                      placeholder="Enter your account password"
                    />
                    {updatePinForm.formState.errors.password && (
                      <p className="mt-1 text-sm text-red-600">
                        {updatePinForm.formState.errors.password.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="currentPin" className="block text-sm font-medium text-gray-700">
                      Current PIN
                    </label>
                    <div className="mt-1 relative">
                      <input
                        {...updatePinForm.register('currentPin', {
                          required: 'Current PIN is required',
                          pattern: {
                            value: /^[0-9]{4}$/,
                            message: 'PIN must be exactly 4 digits'
                          }
                        })}
                        type={showPins.currentPin ? 'text' : 'password'}
                        maxLength="4"
                        className="input-field pr-10"
                        placeholder="Enter current PIN"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => togglePinVisibility('currentPin')}
                      >
                        {showPins.currentPin ? (
                          <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                        ) : (
                          <EyeIcon className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    {updatePinForm.formState.errors.currentPin && (
                      <p className="mt-1 text-sm text-red-600">
                        {updatePinForm.formState.errors.currentPin.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="newPin" className="block text-sm font-medium text-gray-700">
                      New PIN
                    </label>
                    <div className="mt-1 relative">
                      <input
                        {...updatePinForm.register('newPin', {
                          required: 'New PIN is required',
                          pattern: {
                            value: /^[0-9]{4}$/,
                            message: 'PIN must be exactly 4 digits'
                          },
                          validate: value => 
                            value !== updatePinForm.watch('currentPin') || 'New PIN must be different from current PIN'
                        })}
                        type={showPins.newPin ? 'text' : 'password'}
                        maxLength="4"
                        className="input-field pr-10"
                        placeholder="Enter new PIN"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => togglePinVisibility('newPin')}
                      >
                        {showPins.newPin ? (
                          <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                        ) : (
                          <EyeIcon className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    {updatePinForm.formState.errors.newPin && (
                      <p className="mt-1 text-sm text-red-600">
                        {updatePinForm.formState.errors.newPin.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <label htmlFor="confirmNewPin" className="block text-sm font-medium text-gray-700">
                      Confirm New PIN
                    </label>
                    <div className="mt-1 relative">
                      <input
                        {...updatePinForm.register('confirmNewPin', {
                          required: 'Please confirm your new PIN',
                          validate: value => 
                            value === updatePinForm.watch('newPin') || 'PINs do not match'
                        })}
                        type={showPins.confirmPin ? 'text' : 'password'}
                        maxLength="4"
                        className="input-field pr-10"
                        placeholder="Confirm new PIN"
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => togglePinVisibility('confirmPin')}
                      >
                        {showPins.confirmPin ? (
                          <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                        ) : (
                          <EyeIcon className="h-5 w-5 text-gray-400" />
                        )}
                      </button>
                    </div>
                    {updatePinForm.formState.errors.confirmNewPin && (
                      <p className="mt-1 text-sm text-red-600">
                        {updatePinForm.formState.errors.confirmNewPin.message}
                      </p>
                    )}
                  </div>

                  <button
                    type="submit"
                    disabled={isLoading}
                    className="btn-primary w-full flex justify-center items-center"
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Updating PIN...
                      </>
                    ) : (
                      'Update PIN'
                    )}
                  </button>
                </form>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AccountManagement;
