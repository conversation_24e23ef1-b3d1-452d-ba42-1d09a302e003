import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { accountAPI } from '../../services/api';
import Sidebar from '../dashboard/Sidebar';
import { ArrowDownIcon } from '@heroicons/react/24/outline';

const CashDeposit = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm();

  const onSubmit = async (data) => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      await accountAPI.cashDeposit({
        pin: data.pin,
        amount: parseFloat(data.amount),
      });
      
      setSuccess('Cash deposited successfully!');
      reset();
      
      // Redirect to dashboard after 2 seconds
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (err) {
      setError(err.response?.data || 'Failed to deposit cash. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 flex">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden md:ml-64">
        {/* Header */}
        <header className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-200/50">
          <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              <div className="bg-green-100 rounded-xl p-3 mr-4">
                <ArrowDownIcon className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Cash Deposit</h1>
                <p className="text-lg text-gray-600">Add money to your account securely</p>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-2xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
              <div className="bg-gradient-to-r from-green-50 to-blue-50 px-6 py-4 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-900">Deposit Funds</h2>
                <p className="text-sm text-gray-600 mt-1">Enter the amount you want to deposit</p>
              </div>
              <div className="p-6">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    {error}
                  </div>
                )}
                
                {success && (
                  <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    {success}
                  </div>
                )}

                <div>
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                    Deposit Amount (₹)
                  </label>
                  <input
                    {...register('amount', {
                      required: 'Amount is required',
                      min: {
                        value: 100,
                        message: 'Minimum deposit amount is ₹100'
                      },
                      max: {
                        value: 100000,
                        message: 'Maximum deposit amount is ₹1,00,000'
                      },
                      pattern: {
                        value: /^[0-9]+(\.[0-9]{1,2})?$/,
                        message: 'Please enter a valid amount'
                      }
                    })}
                    type="number"
                    step="0.01"
                    min="100"
                    max="100000"
                    className="input-field mt-1"
                    placeholder="Enter amount to deposit"
                  />
                  {errors.amount && (
                    <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
                  )}
                  <p className="mt-1 text-sm text-gray-500">
                    Amount must be between ₹100 and ₹1,00,000
                  </p>
                </div>

                <div>
                  <label htmlFor="pin" className="block text-sm font-medium text-gray-700">
                    Account PIN
                  </label>
                  <input
                    {...register('pin', {
                      required: 'PIN is required',
                      pattern: {
                        value: /^[0-9]{4}$/,
                        message: 'PIN must be 4 digits'
                      }
                    })}
                    type="password"
                    maxLength="4"
                    className="input-field mt-1"
                    placeholder="Enter your 4-digit PIN"
                  />
                  {errors.pin && (
                    <p className="mt-1 text-sm text-red-600">{errors.pin.message}</p>
                  )}
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">Important Notes:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Minimum deposit amount is ₹100</li>
                    <li>• Maximum deposit amount is ₹1,00,000 per transaction</li>
                    <li>• Amount will be credited to your account immediately</li>
                    <li>• You will receive a confirmation message upon successful deposit</li>
                  </ul>
                </div>

                <div className="flex space-x-4">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="btn-primary flex-1 flex justify-center items-center"
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      'Deposit Cash'
                    )}
                  </button>
                  
                  <button
                    type="button"
                    onClick={() => navigate('/dashboard')}
                    className="btn-secondary flex-1"
                  >
                    Cancel
                  </button>
                </div>
              </form>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default CashDeposit;
