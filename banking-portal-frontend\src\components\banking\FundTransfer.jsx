import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { accountAPI } from '../../services/api';
import Sidebar from '../dashboard/Sidebar';
import { ArrowsRightLeftIcon } from '@heroicons/react/24/outline';

const FundTransfer = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm();

  const onSubmit = async (data) => {
    setIsLoading(true);
    setError('');
    setSuccess('');

    try {
      await accountAPI.fundTransfer({
        targetAccountNumber: data.targetAccountNumber,
        pin: data.pin,
        amount: parseFloat(data.amount),
      });
      
      setSuccess('Fund transfer completed successfully!');
      reset();
      
      // Redirect to dashboard after 2 seconds
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (err) {
      setError(err.response?.data || 'Failed to transfer funds. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden md:ml-64">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center">
              <ArrowsRightLeftIcon className="h-8 w-8 text-blue-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Fund Transfer</h1>
                <p className="text-sm text-gray-600">Send money to another account</p>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-2xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div className="card">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {error && (
                  <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    {error}
                  </div>
                )}
                
                {success && (
                  <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    {success}
                  </div>
                )}

                <div>
                  <label htmlFor="targetAccountNumber" className="block text-sm font-medium text-gray-700">
                    Recipient Account Number
                  </label>
                  <input
                    {...register('targetAccountNumber', {
                      required: 'Recipient account number is required',
                      minLength: {
                        value: 6,
                        message: 'Account number must be at least 6 characters'
                      },
                      pattern: {
                        value: /^[A-Za-z0-9]+$/,
                        message: 'Account number can only contain letters and numbers'
                      }
                    })}
                    type="text"
                    className="input-field mt-1"
                    placeholder="Enter recipient's account number"
                  />
                  {errors.targetAccountNumber && (
                    <p className="mt-1 text-sm text-red-600">{errors.targetAccountNumber.message}</p>
                  )}
                </div>

                <div>
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700">
                    Transfer Amount (₹)
                  </label>
                  <input
                    {...register('amount', {
                      required: 'Amount is required',
                      min: {
                        value: 100,
                        message: 'Minimum transfer amount is ₹100'
                      },
                      max: {
                        value: 100000,
                        message: 'Maximum transfer amount is ₹1,00,000'
                      },
                      pattern: {
                        value: /^[0-9]+(\.[0-9]{1,2})?$/,
                        message: 'Please enter a valid amount'
                      }
                    })}
                    type="number"
                    step="0.01"
                    min="100"
                    max="100000"
                    className="input-field mt-1"
                    placeholder="Enter amount to transfer"
                  />
                  {errors.amount && (
                    <p className="mt-1 text-sm text-red-600">{errors.amount.message}</p>
                  )}
                  <p className="mt-1 text-sm text-gray-500">
                    Amount must be between ₹100 and ₹1,00,000
                  </p>
                </div>

                <div>
                  <label htmlFor="pin" className="block text-sm font-medium text-gray-700">
                    Account PIN
                  </label>
                  <input
                    {...register('pin', {
                      required: 'PIN is required',
                      pattern: {
                        value: /^[0-9]{4}$/,
                        message: 'PIN must be 4 digits'
                      }
                    })}
                    type="password"
                    maxLength="4"
                    className="input-field mt-1"
                    placeholder="Enter your 4-digit PIN"
                  />
                  {errors.pin && (
                    <p className="mt-1 text-sm text-red-600">{errors.pin.message}</p>
                  )}
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-blue-900 mb-2">Transfer Guidelines:</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Verify the recipient's account number before proceeding</li>
                    <li>• Minimum transfer amount is ₹100</li>
                    <li>• Maximum transfer amount is ₹1,00,000 per transaction</li>
                    <li>• Ensure you have sufficient balance in your account</li>
                    <li>• Transfer will be processed immediately</li>
                    <li>• You will receive a confirmation message upon successful transfer</li>
                  </ul>
                </div>

                <div className="flex space-x-4">
                  <button
                    type="submit"
                    disabled={isLoading}
                    className="btn-primary flex-1 flex justify-center items-center"
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing Transfer...
                      </>
                    ) : (
                      'Transfer Funds'
                    )}
                  </button>
                  
                  <button
                    type="button"
                    onClick={() => navigate('/dashboard')}
                    className="btn-secondary flex-1"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default FundTransfer;
