import React, { useState, useEffect } from 'react';
import { accountAPI } from '../../services/api';
import Sidebar from '../dashboard/Sidebar';
import {
  ClockIcon,
  ArrowDownIcon,
  ArrowUpIcon,
  ArrowsRightLeftIcon,
  FunnelIcon,
} from '@heroicons/react/24/outline';

const Transactions = () => {
  const [transactions, setTransactions] = useState([]);
  const [filteredTransactions, setFilteredTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('ALL');

  useEffect(() => {
    fetchTransactions();
  }, []);

  useEffect(() => {
    filterTransactions();
  }, [transactions, filter]);

  const fetchTransactions = async () => {
    try {
      const response = await accountAPI.getTransactions();
      const transactionData = JSON.parse(response.data);
      setTransactions(transactionData);
    } catch (err) {
      setError('Failed to load transactions');
      console.error('Error fetching transactions:', err);
    } finally {
      setLoading(false);
    }
  };

  const filterTransactions = () => {
    if (filter === 'ALL') {
      setFilteredTransactions(transactions);
    } else {
      setFilteredTransactions(transactions.filter(t => t.transactionType === filter));
    }
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'CASH_DEPOSIT':
        return <ArrowDownIcon className="h-5 w-5 text-green-600" />;
      case 'CASH_WITHDRAWAL':
        return <ArrowUpIcon className="h-5 w-5 text-red-600" />;
      case 'CASH_TRANSFER':
        return <ArrowsRightLeftIcon className="h-5 w-5 text-blue-600" />;
      default:
        return <ArrowsRightLeftIcon className="h-5 w-5 text-gray-600" />;
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'CASH_DEPOSIT':
        return 'text-green-600 bg-green-50';
      case 'CASH_WITHDRAWAL':
        return 'text-red-600 bg-red-50';
      case 'CASH_TRANSFER':
        return 'text-blue-600 bg-blue-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTransactionDescription = (transaction) => {
    switch (transaction.transactionType) {
      case 'CASH_DEPOSIT':
        return 'Cash Deposit';
      case 'CASH_WITHDRAWAL':
        return 'Cash Withdrawal';
      case 'CASH_TRANSFER':
        return `Transfer to ${transaction.targetAccountNumber}`;
      default:
        return 'Transaction';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex">
        <Sidebar />
        <div className="flex-1 flex flex-col overflow-hidden md:ml-64">
          <div className="flex-1 flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex">
      <Sidebar />
      
      <div className="flex-1 flex flex-col overflow-hidden md:ml-64">
        {/* Header */}
        <header className="bg-white shadow-sm border-b border-gray-200">
          <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <ClockIcon className="h-8 w-8 text-purple-600 mr-3" />
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Transaction History</h1>
                  <p className="text-sm text-gray-600">View all your transactions</p>
                </div>
              </div>
              
              {/* Filter */}
              <div className="flex items-center space-x-2">
                <FunnelIcon className="h-5 w-5 text-gray-400" />
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value)}
                  className="input-field py-1 text-sm"
                >
                  <option value="ALL">All Transactions</option>
                  <option value="CASH_DEPOSIT">Deposits</option>
                  <option value="CASH_WITHDRAWAL">Withdrawals</option>
                  <option value="CASH_TRANSFER">Transfers</option>
                </select>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            {error && (
              <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                {error}
              </div>
            )}

            <div className="card">
              {filteredTransactions.length === 0 ? (
                <div className="text-center py-12">
                  <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    {filter === 'ALL' 
                      ? "You haven't made any transactions yet." 
                      : `No ${filter.toLowerCase().replace('_', ' ')} transactions found.`
                    }
                  </p>
                </div>
              ) : (
                <div className="overflow-hidden">
                  <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                      {filteredTransactions.length} Transaction{filteredTransactions.length !== 1 ? 's' : ''}
                    </h3>
                    
                    <div className="space-y-4">
                      {filteredTransactions.map((transaction) => (
                        <div
                          key={transaction.id}
                          className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                        >
                          <div className="flex items-center space-x-4">
                            <div className={`flex-shrink-0 p-2 rounded-full ${getTransactionColor(transaction.transactionType)}`}>
                              {getTransactionIcon(transaction.transactionType)}
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-medium text-gray-900">
                                {getTransactionDescription(transaction)}
                              </p>
                              <p className="text-sm text-gray-500">
                                {formatDate(transaction.transactionDate)}
                              </p>
                              {transaction.targetAccountNumber && (
                                <p className="text-xs text-gray-400">
                                  To: {transaction.targetAccountNumber}
                                </p>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex-shrink-0 text-right">
                            <p className={`text-sm font-medium ${
                              transaction.transactionType === 'CASH_WITHDRAWAL' 
                                ? 'text-red-600' 
                                : 'text-green-600'
                            }`}>
                              {transaction.transactionType === 'CASH_WITHDRAWAL' ? '-' : '+'}
                              {formatCurrency(transaction.amount)}
                            </p>
                            <p className="text-xs text-gray-500">
                              ID: {transaction.id}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default Transactions;
