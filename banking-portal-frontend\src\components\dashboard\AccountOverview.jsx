import React from 'react';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

const AccountOverview = ({ userDetails }) => {
  const [showBalance, setShowBalance] = React.useState(true);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount || 0);
  };

  return (
    <div className="space-y-6">
      {/* Account Balance Card */}
      <div className="bg-blue-600 rounded-lg p-6 text-white">
        <div className="flex items-center justify-between mb-4">
          <div>
            <p className="text-blue-100 text-sm">Account Balance</p>
            <div className="flex items-center">
              <p className="text-3xl font-bold">
                {showBalance ? formatCurrency(userDetails?.balance) : '••••••'}
              </p>
              <button
                onClick={() => setShowBalance(!showBalance)}
                className="ml-2 p-1 rounded hover:bg-white/20"
              >
                {showBalance ? (
                  <EyeSlashIcon className="h-4 w-4" />
                ) : (
                  <EyeIcon className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
          <div className="text-right">
            <p className="text-blue-100 text-sm">Account Type</p>
            <p className="font-semibold">{userDetails?.accountType || 'Savings'}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 mt-4">
          <div>
            <p className="text-blue-100 text-xs">Account Number</p>
            <p className="font-medium">{userDetails?.accountNumber}</p>
          </div>
          <div>
            <p className="text-blue-100 text-xs">IFSC Code</p>
            <p className="font-medium">{userDetails?.ifscCode}</p>
          </div>
        </div>
      </div>

      {/* Account Details */}
      <div className="bg-white rounded-lg shadow border">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Account Information</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-500">Account Holder</label>
              <p className="mt-1 text-gray-900">{userDetails?.name}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Email</label>
              <p className="mt-1 text-gray-900">{userDetails?.email}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Phone</label>
              <p className="mt-1 text-gray-900">
                {userDetails?.countryCode} {userDetails?.phoneNumber}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Branch</label>
              <p className="mt-1 text-gray-900">{userDetails?.branch}</p>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-500">Address</label>
              <p className="mt-1 text-gray-900">{userDetails?.address}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountOverview;
