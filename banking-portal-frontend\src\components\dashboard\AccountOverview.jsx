import React from 'react';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

const AccountOverview = ({ userDetails }) => {
  const [showBalance, setShowBalance] = React.useState(true);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount || 0);
  };

  return (
    <div className="space-y-8">
      {/* Account Balance Card */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 rounded-3xl p-8 text-white shadow-2xl">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 right-0 w-64 h-64 bg-white rounded-full -mr-32 -mt-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white rounded-full -ml-24 -mb-24"></div>
        </div>

        <div className="relative z-10">
          <div className="flex items-start justify-between mb-8">
            <div className="flex-1">
              <div className="flex items-center mb-2">
                <p className="text-blue-100 text-sm font-medium">Available Balance</p>
                <button
                  onClick={() => setShowBalance(!showBalance)}
                  className="ml-3 p-2 rounded-full hover:bg-white/20 transition-all duration-200 transform hover:scale-110"
                >
                  {showBalance ? (
                    <EyeSlashIcon className="h-5 w-5" />
                  ) : (
                    <EyeIcon className="h-5 w-5" />
                  )}
                </button>
              </div>
              <div className="flex items-baseline">
                <p className="text-5xl font-bold tracking-tight">
                  {showBalance ? formatCurrency(userDetails?.balance) : '••••••••'}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="bg-white/20 rounded-xl p-3 backdrop-blur-sm">
                <p className="text-blue-100 text-xs font-medium">Account Type</p>
                <p className="text-lg font-bold">{userDetails?.accountType || 'Savings'}</p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-6">
            <div className="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
              <p className="text-blue-100 text-xs font-medium mb-1">Account Number</p>
              <p className="font-semibold text-lg">{userDetails?.accountNumber}</p>
            </div>
            <div className="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
              <p className="text-blue-100 text-xs font-medium mb-1">IFSC Code</p>
              <p className="font-semibold text-lg">{userDetails?.ifscCode}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
          <div className="flex items-center">
            <div className="bg-green-100 rounded-xl p-3">
              <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">This Month</p>
              <p className="text-2xl font-bold text-gray-900">+₹25,430</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
          <div className="flex items-center">
            <div className="bg-red-100 rounded-xl p-3">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Spent</p>
              <p className="text-2xl font-bold text-gray-900">₹12,850</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
          <div className="flex items-center">
            <div className="bg-blue-100 rounded-xl p-3">
              <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Transactions</p>
              <p className="text-2xl font-bold text-gray-900">47</p>
            </div>
          </div>
        </div>
      </div>

      {/* Account Details */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200">
          <h3 className="text-xl font-bold text-gray-900">Account Information</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-semibold text-gray-500 mb-1">Account Holder</label>
                <p className="text-lg font-medium text-gray-900">{userDetails?.name}</p>
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-500 mb-1">Email Address</label>
                <p className="text-lg font-medium text-gray-900">{userDetails?.email}</p>
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-500 mb-1">Phone Number</label>
                <p className="text-lg font-medium text-gray-900">
                  {userDetails?.countryCode} {userDetails?.phoneNumber}
                </p>
              </div>
            </div>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-semibold text-gray-500 mb-1">Branch</label>
                <p className="text-lg font-medium text-gray-900">{userDetails?.branch}</p>
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-500 mb-1">Account Status</label>
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                  Active
                </span>
              </div>
              <div>
                <label className="block text-sm font-semibold text-gray-500 mb-1">Member Since</label>
                <p className="text-lg font-medium text-gray-900">January 2024</p>
              </div>
            </div>
            <div className="md:col-span-2">
              <label className="block text-sm font-semibold text-gray-500 mb-1">Address</label>
              <p className="text-lg font-medium text-gray-900">{userDetails?.address}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountOverview;
