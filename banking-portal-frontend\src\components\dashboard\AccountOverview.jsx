import React from 'react';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

const AccountOverview = ({ userDetails }) => {
  const [showBalance, setShowBalance] = React.useState(true);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount || 0);
  };

  return (
    <div className="space-y-6">
      {/* Account Balance Card */}
      <div className="card gradient-bg text-white">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-blue-100 text-sm font-medium">Account Balance</p>
            <div className="flex items-center mt-2">
              <p className="text-3xl font-bold">
                {showBalance ? formatCurrency(userDetails?.balance) : '••••••'}
              </p>
              <button
                onClick={() => setShowBalance(!showBalance)}
                className="ml-3 p-1 rounded-full hover:bg-white/20 transition-colors"
              >
                {showBalance ? (
                  <EyeSlashIcon className="h-5 w-5" />
                ) : (
                  <EyeIcon className="h-5 w-5" />
                )}
              </button>
            </div>
          </div>
          <div className="text-right">
            <p className="text-blue-100 text-sm">Account Type</p>
            <p className="text-lg font-semibold">{userDetails?.accountType || 'Savings'}</p>
          </div>
        </div>
        
        <div className="mt-6 grid grid-cols-2 gap-4">
          <div>
            <p className="text-blue-100 text-xs">Account Number</p>
            <p className="font-medium">{userDetails?.accountNumber}</p>
          </div>
          <div>
            <p className="text-blue-100 text-xs">IFSC Code</p>
            <p className="font-medium">{userDetails?.ifscCode}</p>
          </div>
        </div>
      </div>

      {/* Account Details */}
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Account Holder</label>
            <p className="mt-1 text-sm text-gray-900">{userDetails?.name}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Email</label>
            <p className="mt-1 text-sm text-gray-900">{userDetails?.email}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Phone</label>
            <p className="mt-1 text-sm text-gray-900">
              {userDetails?.countryCode} {userDetails?.phoneNumber}
            </p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Branch</label>
            <p className="mt-1 text-sm text-gray-900">{userDetails?.branch}</p>
          </div>
          <div className="md:col-span-2">
            <label className="block text-sm font-medium text-gray-700">Address</label>
            <p className="mt-1 text-sm text-gray-900">{userDetails?.address}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountOverview;
