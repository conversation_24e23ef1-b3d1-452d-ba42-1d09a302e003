import React from 'react';
import { Link } from 'react-router-dom';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  ArrowsRightLeftIcon,
  ClockIcon,
  CreditCardIcon,
  UserIcon,
} from '@heroicons/react/24/outline';

const QuickActions = () => {
  const actions = [
    {
      name: 'Deposit Cash',
      description: 'Add money to your account',
      href: '/deposit',
      icon: ArrowDownIcon,
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      name: 'Withdraw Cash',
      description: 'Withdraw money from your account',
      href: '/withdraw',
      icon: ArrowUpIcon,
      color: 'bg-red-500 hover:bg-red-600',
    },
    {
      name: 'Transfer Funds',
      description: 'Send money to another account',
      href: '/transfer',
      icon: ArrowsRightLeftIcon,
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      name: 'View Transactions',
      description: 'Check your transaction history',
      href: '/transactions',
      icon: ClockIcon,
      color: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      name: 'Manage PIN',
      description: 'Create or update your PIN',
      href: '/account',
      icon: CreditCardIcon,
      color: 'bg-orange-500 hover:bg-orange-600',
    },
    {
      name: 'Profile Settings',
      description: 'Update your profile information',
      href: '/profile',
      icon: UserIcon,
      color: 'bg-gray-500 hover:bg-gray-600',
    },
  ];

  return (
    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
      <div className="bg-gradient-to-r from-gray-50 to-blue-50 px-6 py-4 border-b border-gray-200">
        <h3 className="text-xl font-bold text-gray-900">Quick Actions</h3>
        <p className="text-sm text-gray-600 mt-1">Manage your account easily</p>
      </div>
      <div className="p-6 space-y-4">
        {actions.map((action) => (
          <Link
            key={action.name}
            to={action.href}
            className="flex items-center p-4 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 group transform hover:scale-[1.02] bg-gradient-to-r hover:from-blue-50 hover:to-purple-50"
          >
            <div className={`flex-shrink-0 p-3 rounded-xl ${action.color} transition-all duration-200 group-hover:scale-110 shadow-lg`}>
              <action.icon className="h-6 w-6 text-white" />
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-semibold text-gray-900 group-hover:text-blue-700 transition-colors duration-200">
                {action.name}
              </p>
              <p className="text-xs text-gray-500 mt-1">{action.description}</p>
            </div>
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-all duration-200 transform group-hover:translate-x-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </Link>
        ))}

        {/* Emergency Contact */}
        <div className="mt-6 p-4 bg-gradient-to-r from-red-50 to-orange-50 rounded-xl border border-red-200">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm font-semibold text-red-800">Need Help?</p>
              <p className="text-xs text-red-600">Call 24/7 Support: 1800-123-4567</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickActions;
