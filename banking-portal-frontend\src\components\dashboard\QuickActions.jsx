import React from 'react';
import { Link } from 'react-router-dom';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  ArrowsRightLeftIcon,
  ClockIcon,
  CreditCardIcon,
  UserIcon,
} from '@heroicons/react/24/outline';

const QuickActions = () => {
  const actions = [
    {
      name: 'Deposit Cash',
      description: 'Add money to your account',
      href: '/deposit',
      icon: ArrowDownIcon,
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      name: 'Withdraw Cash',
      description: 'Withdraw money from your account',
      href: '/withdraw',
      icon: ArrowUpIcon,
      color: 'bg-red-500 hover:bg-red-600',
    },
    {
      name: 'Transfer Funds',
      description: 'Send money to another account',
      href: '/transfer',
      icon: ArrowsRightLeftIcon,
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      name: 'View Transactions',
      description: 'Check your transaction history',
      href: '/transactions',
      icon: ClockIcon,
      color: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      name: 'Manage PIN',
      description: 'Create or update your PIN',
      href: '/account',
      icon: CreditCardIcon,
      color: 'bg-orange-500 hover:bg-orange-600',
    },
    {
      name: 'Profile Settings',
      description: 'Update your profile information',
      href: '/profile',
      icon: UserIcon,
      color: 'bg-gray-500 hover:bg-gray-600',
    },
  ];

  return (
    <div className="bg-white rounded-lg shadow border">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Quick Actions</h3>
      </div>
      <div className="p-6 space-y-3">
        {actions.map((action) => (
          <Link
            key={action.name}
            to={action.href}
            className="flex items-center p-3 rounded border border-gray-200 hover:border-gray-300 hover:bg-gray-50 transition-colors"
          >
            <div className={`flex-shrink-0 p-2 rounded ${action.color}`}>
              <action.icon className="h-4 w-4 text-white" />
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-gray-900">{action.name}</p>
              <p className="text-xs text-gray-500">{action.description}</p>
            </div>
            <svg
              className="h-4 w-4 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default QuickActions;
