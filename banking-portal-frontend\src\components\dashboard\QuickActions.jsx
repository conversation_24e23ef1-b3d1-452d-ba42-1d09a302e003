import React from 'react';
import { Link } from 'react-router-dom';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  ArrowsRightLeftIcon,
  ClockIcon,
  CreditCardIcon,
  UserIcon,
} from '@heroicons/react/24/outline';

const QuickActions = () => {
  const actions = [
    {
      name: 'Deposit Cash',
      description: 'Add money to your account',
      href: '/deposit',
      icon: ArrowDownIcon,
      color: 'bg-green-500 hover:bg-green-600',
    },
    {
      name: 'Withdraw Cash',
      description: 'Withdraw money from your account',
      href: '/withdraw',
      icon: ArrowUpIcon,
      color: 'bg-red-500 hover:bg-red-600',
    },
    {
      name: 'Transfer Funds',
      description: 'Send money to another account',
      href: '/transfer',
      icon: ArrowsRightLeftIcon,
      color: 'bg-blue-500 hover:bg-blue-600',
    },
    {
      name: 'View Transactions',
      description: 'Check your transaction history',
      href: '/transactions',
      icon: ClockIcon,
      color: 'bg-purple-500 hover:bg-purple-600',
    },
    {
      name: 'Manage PIN',
      description: 'Create or update your PIN',
      href: '/account',
      icon: CreditCardIcon,
      color: 'bg-orange-500 hover:bg-orange-600',
    },
    {
      name: 'Profile Settings',
      description: 'Update your profile information',
      href: '/profile',
      icon: UserIcon,
      color: 'bg-gray-500 hover:bg-gray-600',
    },
  ];

  return (
    <div className="card">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
      <div className="space-y-3">
        {actions.map((action) => (
          <Link
            key={action.name}
            to={action.href}
            className="flex items-center p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-sm transition-all duration-200 group"
          >
            <div className={`flex-shrink-0 p-2 rounded-lg ${action.color} transition-colors duration-200`}>
              <action.icon className="h-5 w-5 text-white" />
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-gray-900 group-hover:text-primary-600 transition-colors duration-200">
                {action.name}
              </p>
              <p className="text-xs text-gray-500">{action.description}</p>
            </div>
            <div className="flex-shrink-0">
              <svg
                className="h-4 w-4 text-gray-400 group-hover:text-primary-600 transition-colors duration-200"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default QuickActions;
