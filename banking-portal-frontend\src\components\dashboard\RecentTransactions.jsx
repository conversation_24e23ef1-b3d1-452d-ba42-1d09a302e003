import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { accountAPI } from '../../services/api';
import {
  ArrowDownIcon,
  ArrowUpIcon,
  ArrowsRightLeftIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

const RecentTransactions = () => {
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchTransactions();
  }, []);

  const fetchTransactions = async () => {
    try {
      const response = await accountAPI.getTransactions();
      // Get only the last 5 transactions
      setTransactions(JSON.parse(response.data).slice(0, 5));
    } catch (err) {
      setError('Failed to load transactions');
      console.error('Error fetching transactions:', err);
    } finally {
      setLoading(false);
    }
  };

  const getTransactionIcon = (type) => {
    switch (type) {
      case 'CASH_DEPOSIT':
        return <ArrowDownIcon className="h-4 w-4 text-green-600" />;
      case 'CASH_WITHDRAWAL':
        return <ArrowUpIcon className="h-4 w-4 text-red-600" />;
      case 'CASH_TRANSFER':
        return <ArrowsRightLeftIcon className="h-4 w-4 text-blue-600" />;
      default:
        return <ArrowsRightLeftIcon className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTransactionColor = (type) => {
    switch (type) {
      case 'CASH_DEPOSIT':
        return 'text-green-600';
      case 'CASH_WITHDRAWAL':
        return 'text-red-600';
      case 'CASH_TRANSFER':
        return 'text-blue-600';
      default:
        return 'text-gray-600';
    }
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(amount);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getTransactionDescription = (transaction) => {
    switch (transaction.transactionType) {
      case 'CASH_DEPOSIT':
        return 'Cash Deposit';
      case 'CASH_WITHDRAWAL':
        return 'Cash Withdrawal';
      case 'CASH_TRANSFER':
        return `Transfer to ${transaction.targetAccountNumber}`;
      default:
        return 'Transaction';
    }
  };

  if (loading) {
    return (
      <div className="card">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Transactions</h3>
        <div className="animate-pulse space-y-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center space-x-4">
              <div className="rounded-full bg-gray-200 h-10 w-10"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div className="h-4 bg-gray-200 rounded w-20"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
        <Link
          to="/transactions"
          className="text-sm text-primary-600 hover:text-primary-500 font-medium"
        >
          View all
        </Link>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4">
          {error}
        </div>
      )}

      {transactions.length === 0 ? (
        <div className="text-center py-8">
          <div className="mx-auto h-8 w-8 text-gray-400">
            <ClockIcon className="h-8 w-8" />
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions</h3>
          <p className="mt-1 text-sm text-gray-500">
            You haven't made any transactions yet.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {transactions.map((transaction) => (
            <div
              key={transaction.id}
              className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <div className="flex-shrink-0 p-2 bg-gray-100 rounded-full">
                {getTransactionIcon(transaction.transactionType)}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {getTransactionDescription(transaction)}
                </p>
                <p className="text-sm text-gray-500">
                  {formatDate(transaction.transactionDate)}
                </p>
              </div>
              <div className="flex-shrink-0">
                <p className={`text-sm font-medium ${getTransactionColor(transaction.transactionType)}`}>
                  {transaction.transactionType === 'CASH_WITHDRAWAL' ? '-' : '+'}
                  {formatCurrency(transaction.amount)}
                </p>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default RecentTransactions;
