import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import {
  HomeIcon,
  CreditCardIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ArrowsRightLeftIcon,
  ClockIcon,
  UserIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/react/24/outline';

const Sidebar = ({ userDetails }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { logout } = useAuth();

  const navigation = [
    { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
    { name: 'Account', href: '/account', icon: CreditCardIcon },
    { name: 'Deposit', href: '/deposit', icon: ArrowDownIcon },
    { name: 'Withdraw', href: '/withdraw', icon: ArrowUpIcon },
    { name: 'Transfer', href: '/transfer', icon: ArrowsRightLeftIcon },
    { name: 'Transactions', href: '/transactions', icon: ClockIcon },
    { name: 'Profile', href: '/profile', icon: UserIcon },
    { name: 'Settings', href: '/settings', icon: Cog6ToothIcon },
  ];

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  return (
    <div className="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0 z-50">
      <div className="flex-1 flex flex-col min-h-0 bg-white/95 backdrop-blur-sm border-r border-gray-200/50 shadow-xl">
        {/* Logo */}
        <div className="flex items-center h-20 flex-shrink-0 px-6 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="flex items-center">
            <div className="h-10 w-10 bg-white rounded-xl flex items-center justify-center shadow-lg">
              <svg className="h-6 w-6 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
              </svg>
            </div>
            <span className="ml-3 text-white font-bold text-xl">OneStopBank</span>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex-1 flex flex-col overflow-y-auto py-6">
          <nav className="flex-1 px-4 space-y-2">
            {navigation.map((item) => {
              const isActive = location.pathname === item.href;
              return (
                <Link
                  key={item.name}
                  to={item.href}
                  className={`${
                    isActive
                      ? 'bg-gradient-to-r from-blue-50 to-purple-50 border-r-4 border-blue-500 text-blue-700 shadow-sm'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  } group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-200 transform hover:scale-[1.02]`}
                >
                  <item.icon
                    className={`${
                      isActive ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-600'
                    } mr-4 flex-shrink-0 h-5 w-5`}
                    aria-hidden="true"
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User Info Card */}
          <div className="mx-4 mb-4 p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl border border-gray-200">
            <div className="flex items-center">
              <div className="h-10 w-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-sm">
                  {userDetails?.name?.charAt(0) || 'U'}
                </span>
              </div>
              <div className="ml-3 flex-1 min-w-0">
                <p className="text-sm font-semibold text-gray-900 truncate">
                  {userDetails?.name || 'User'}
                </p>
                <p className="text-xs text-gray-500 truncate">
                  Account: {userDetails?.accountNumber?.slice(-4) || '****'}
                </p>
              </div>
            </div>
          </div>

          {/* Logout Button */}
          <div className="px-4">
            <button
              onClick={handleLogout}
              className="group flex items-center w-full px-4 py-3 text-sm font-semibold text-gray-600 rounded-xl hover:bg-red-50 hover:text-red-600 transition-all duration-200 transform hover:scale-[1.02]"
            >
              <ArrowRightOnRectangleIcon
                className="text-gray-400 group-hover:text-red-500 mr-4 flex-shrink-0 h-5 w-5"
                aria-hidden="true"
              />
              Sign Out
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
