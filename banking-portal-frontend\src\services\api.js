import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8180/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API calls
export const authAPI = {
  register: (userData) => api.post('/users/register', userData),
  login: (credentials) => api.post('/users/login', credentials),
  logout: () => api.post('/users/logout'),
  generateOtp: (accountNumber) => api.post('/users/generate-otp', { accountNumber }),
  verifyOtp: (otpData) => api.post('/users/verify-otp', otpData),
  resetPassword: (resetData) => api.post('/users/reset-password', resetData),
};

// Account API calls
export const accountAPI = {
  createPin: (pinData) => api.post('/accounts/create-pin', pinData),
  updatePin: (pinData) => api.put('/accounts/update-pin', pinData),
  cashDeposit: (depositData) => api.post('/accounts/cash-deposit', depositData),
  cashWithdrawal: (withdrawalData) => api.post('/accounts/cash-withdrawal', withdrawalData),
  fundTransfer: (transferData) => api.post('/accounts/fund-transfer', transferData),
  getTransactions: () => api.get('/accounts/transactions'),
};

// Dashboard API calls
export const dashboardAPI = {
  getUserDetails: () => api.get('/dashboard/user-details'),
  getAccountSummary: () => api.get('/dashboard/account-summary'),
};

// Token API calls
export const tokenAPI = {
  validateToken: (token) => api.post('/tokens/validate', { token }),
  invalidateToken: (token) => api.post('/tokens/invalidate', { token }),
};

export default api;
