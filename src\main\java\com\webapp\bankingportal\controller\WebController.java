package com.webapp.bankingportal.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import com.webapp.bankingportal.dto.UserRegistrationDto;
import com.webapp.bankingportal.entity.User;
import com.webapp.bankingportal.service.UserService;

import jakarta.servlet.http.HttpSession;

@Controller
public class WebController {

    @Autowired
    private UserService userService;

    @GetMapping("/")
    public String home() {
        return "index";
    }

    @GetMapping("/login")
    public String login() {
        return "login";
    }

    @GetMapping("/register")
    public String register(Model model) {
        model.addAttribute("user", new UserRegistrationDto());
        return "register";
    }

    @PostMapping("/register")
    public String registerUser(UserRegistrationDto userDto, RedirectAttributes redirectAttributes) {
        try {
            // Convert DTO to User entity
            User user = new User();
            user.setName(userDto.getName());
            user.setEmail(userDto.getEmail());
            user.setCountryCode(userDto.getCountryCode());
            user.setPhoneNumber(userDto.getPhoneNumber());
            user.setAddress(userDto.getAddress());
            user.setPassword(userDto.getPassword());

            // Call the service method
            var response = userService.registerUser(user);

            if (response.getStatusCode().is2xxSuccessful()) {
                redirectAttributes.addFlashAttribute("success", "Registration successful! Please login.");
                return "redirect:/login";
            } else {
                redirectAttributes.addFlashAttribute("error", "Registration failed. Please try again.");
                return "redirect:/register";
            }
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Registration failed: " + e.getMessage());
            return "redirect:/register";
        }
    }

    @PostMapping("/login")
    public String loginUser(@RequestParam String accountNumber,
            @RequestParam String password,
            HttpSession session,
            RedirectAttributes redirectAttributes) {
        try {
            // Here you would implement login logic
            // For now, just redirect to dashboard
            session.setAttribute("accountNumber", accountNumber);
            return "redirect:/dashboard";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Login failed: " + e.getMessage());
            return "redirect:/login";
        }
    }

    @GetMapping("/dashboard")
    public String dashboard(HttpSession session, Model model) {
        String accountNumber = (String) session.getAttribute("accountNumber");
        if (accountNumber == null) {
            return "redirect:/login";
        }

        model.addAttribute("accountNumber", accountNumber);
        return "dashboard";
    }

    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.invalidate();
        return "redirect:/";
    }
}
