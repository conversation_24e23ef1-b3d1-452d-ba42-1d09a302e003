{"properties": [{"name": "jwt.secret", "type": "java.lang.String", "description": "JWT secret key"}, {"name": "jwt.expiration", "type": "java.lang.Long", "description": "JWT expiration time in milliseconds"}, {"name": "jwt.header", "type": "java.lang.String", "description": "JWT header"}, {"name": "jwt.prefix", "type": "java.lang.String", "description": "JWT prefix"}, {"name": "geo.api.url", "type": "java.lang.String", "description": "Geolocation API URL"}, {"name": "geo.api.key", "type": "java.lang.String", "description": "Geolocation API key"}]}