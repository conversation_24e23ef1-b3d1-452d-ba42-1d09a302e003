<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} ?: 'OneStopBank - Digital Banking Platform'">OneStopBank</title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a th:href="@{/}" style="color: white; text-decoration: none;">
                    🏦 OneStopBank
                </a>
            </div>
            <nav>
                <ul class="nav-links">
                    <li th:if="${session.accountNumber == null}">
                        <a th:href="@{/}">Home</a>
                    </li>
                    <li th:if="${session.accountNumber == null}">
                        <a th:href="@{/login}">Login</a>
                    </li>
                    <li th:if="${session.accountNumber == null}">
                        <a th:href="@{/register}">Register</a>
                    </li>
                    <li th:if="${session.accountNumber != null}">
                        <a th:href="@{/dashboard}">Dashboard</a>
                    </li>
                    <li th:if="${session.accountNumber != null}">
                        <a th:href="@{/logout}">Logout</a>
                    </li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Flash Messages -->
            <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
            <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>
            
            <!-- Page Content -->
            <div th:replace="${content}">
                <!-- Content will be replaced here -->
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 OneStopBank. All rights reserved. | FDIC Insured | Equal Housing Lender</p>
        </div>
    </footer>

    <!-- JavaScript -->
    <script th:src="@{/js/main.js}"></script>
</body>
</html>
