<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Account - OneStopBank</title>
    <link rel="stylesheet" th:href="@{/css/style.css}">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a th:href="@{/}" style="color: white; text-decoration: none;">🏦 OneStopBank</a>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a th:href="@{/}">Home</a></li>
                    <li><a th:href="@{/login}">Already have an account? Sign in</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container" style="max-width: 600px;">
            <!-- Flash Messages -->
            <div th:if="${success}" class="alert alert-success" th:text="${success}"></div>
            <div th:if="${error}" class="alert alert-error" th:text="${error}"></div>
            
            <div class="card">
                <div class="card-header" style="text-align: center;">
                    <h1 class="card-title">Create your account</h1>
                    <p style="color: #666; margin: 0;">Join OneStopBank today</p>
                </div>

                <form th:action="@{/register}" method="post" th:object="${user}">
                    <div class="form-group">
                        <label for="name" class="form-label">Full Name</label>
                        <input type="text" 
                               id="name" 
                               th:field="*{name}"
                               class="form-control" 
                               placeholder="Enter your full name"
                               required>
                    </div>

                    <div class="form-group">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" 
                               id="email" 
                               th:field="*{email}"
                               class="form-control" 
                               placeholder="Enter your email"
                               required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="countryCode" class="form-label">Country Code</label>
                            <select id="countryCode" 
                                    th:field="*{countryCode}"
                                    class="form-control" 
                                    required>
                                <option value="">Select</option>
                                <option value="IN">IN (+91)</option>
                                <option value="US">US (+1)</option>
                                <option value="UK">UK (+44)</option>
                                <option value="CA">CA (+1)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="phoneNumber" class="form-label">Phone Number</label>
                            <input type="tel" 
                                   id="phoneNumber" 
                                   th:field="*{phoneNumber}"
                                   class="form-control" 
                                   placeholder="1234567890"
                                   required>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="address" class="form-label">Address</label>
                        <textarea id="address" 
                                  th:field="*{address}"
                                  class="form-control" 
                                  rows="3"
                                  placeholder="Enter your full address"
                                  required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" 
                               id="password" 
                               th:field="*{password}"
                               class="form-control" 
                               placeholder="Enter your password"
                               minlength="8"
                               required>
                        <small style="color: #666; font-size: 0.85rem;">Password must be at least 8 characters long</small>
                    </div>

                    <div class="form-group">
                        <label for="confirmPassword" class="form-label">Confirm Password</label>
                        <input type="password" 
                               id="confirmPassword" 
                               name="confirmPassword"
                               class="form-control" 
                               placeholder="Confirm your password"
                               required>
                    </div>

                    <button type="submit" class="btn btn-primary btn-full">Create Account</button>

                    <div style="text-align: center; margin-top: 1.5rem; color: #666;">
                        Already have an account? 
                        <a th:href="@{/login}" style="color: #3498db; text-decoration: none; font-weight: 500;">Sign in</a>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 OneStopBank. FDIC Insured. All rights reserved.</p>
        </div>
    </footer>

    <script>
        // Password confirmation validation
        document.getElementById('confirmPassword').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>
